:root {
  --bg: #0a0c10;
  --bg-2: #0f1220;
  --text: #e6e8ef;
  --muted: #9aa3b2;
  --primary: #7c5cff;
  --primary-2: #3aa6ff;
  --accent: #30F3B9;
  --glass: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.2);
  --card: rgba(255, 255, 255, 0.06);
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.35);
  --radius: 16px;
}

[data-theme="light"] {
  --bg: #ffffff;
  --bg-2: #f8fafc;
  --text: #1e293b;
  --muted: #64748b;
  --primary: #4f46e5;
  --primary-2: #06b6d4;
  --accent: #30F3B9;
  --glass: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(148, 163, 184, 0.3);
  --card: rgba(255, 255, 255, 0.9);
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  color: var(--text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark theme background - matching the image */
[data-theme="dark"] body {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e1b4b 25%, #0f172a 50%, #000000 100%);
}

/* Light theme background */
[data-theme="light"] body {
  background: radial-gradient(1200px 800px at 10% 10%, rgba(48, 243, 185, 0.05) 0%, var(--bg) 40%),
    radial-gradient(900px 600px at 90% 10%, rgba(79, 70, 229, 0.08) 0%, transparent 50%),
    linear-gradient(180deg, var(--bg) 0%, var(--bg-2) 100%);
}

/* Loading Screen */
#loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
}

.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.loader-logo {
  width: 80px;
  height: 80px;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.loader-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.loader-progress {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  animation: progress 2s ease-in-out forwards;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes progress {
  0% { width: 0%; }
  100% { width: 100%; }
}

/* Cursor Follower */
.cursor-follower {
  position: fixed;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(124, 92, 255, 0.15) 0%, rgba(48, 243, 185, 0.05) 50%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 10;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  will-change: transform, left, top;
}

/* Floating tech icons removed */

/* Portfolio Modal Styles */
.portfolio-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(5px);
}

.modal-content {
  position: relative;
  background-color: var(--bg);
  margin: 5% auto;
  padding: 30px;
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  width: 80%;
  max-width: 1000px;
  box-shadow: var(--shadow);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  color: var(--muted);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: var(--text);
}

.modal-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 20px;
  margin-bottom: 30px;
}

.modal-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  aspect-ratio: 16/9;
}

.modal-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.modal-image:hover {
  transform: scale(1.05);
}

.modal-title {
  font-size: 24px;
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text);
}

.modal-description {
  color: var(--muted);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .modal-gallery {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    width: 90%;
    padding: 20px;
  }
}

/* Tech icon styles removed */

/* Typography */
h1,
.hero-copy h1 {
  font-family: 'DM Sans', Inter, system-ui, sans-serif;
  font-weight: 700;
}

/* Override for neon heading */
.hero-copy h1.neon-heading {
  font-family: 'RQND Pro', 'DM Sans', sans-serif !important;
  font-size: clamp(3rem, 7vw, 5rem) !important;
  font-weight: 700 !important;
  text-align: center !important;
  color: #fff !important;
  text-shadow: 
    0 0 10px rgba(48, 243, 185, 0.6),
    0 0 20px rgba(48, 243, 185, 0.4),
    0 0 30px rgba(48, 243, 185, 0.3) !important;
  animation: softGlow 3s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite !important;
  letter-spacing: 2px !important;
  line-height: 1.1 !important;
  margin-bottom: 2rem !important;
  text-transform: uppercase !important;
}

h2,
.section-head h2 {
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-weight: 600;
}

h3,
.card h3,
.hero-card h3,
.cta-banner h3,
.faq-question h3 {
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-weight: 500;
}

p,
.hero-copy p,
.section-head p,
.card p,
.faq-answer p {
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] p,
[data-theme="light"] .hero-copy p,
[data-theme="light"] .section-head p,
[data-theme="light"] .card p,
[data-theme="light"] .faq-answer p {
  color: rgba(0, 0, 0, 0.85);
  text-shadow: none;
}

/* Animated background canvas overlay */
canvas#bg {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.container {
  width: min(1200px, calc(100% - 2rem));
  margin: 0 auto;
}

/* Clean minimal navbar */
.navbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="light"] .navbar {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.brand {
  display: inline-flex;
  align-items: center;
  gap: .6rem;
  text-decoration: none;
  color: #fff;
  font-weight: 700;
  letter-spacing: .2px;
  font-size: 1.2rem;
}

[data-theme="light"] .brand {
  color: var(--text);
}

.brand-logo {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  animation: rotate 10s linear infinite;
  filter: drop-shadow(0 0 5px rgba(48, 243, 185, 0.5));
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-links a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: color .2s ease;
  position: relative;
}

.nav-links a:hover {
  color: #fff;
}

.nav-links a.active {
  color: #fff;
}

.nav-links a.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--accent);
  border-radius: 1px;
}

[data-theme="light"] .nav-links a {
  color: rgba(0, 0, 0, 0.8);
}

[data-theme="light"] .nav-links a:hover {
  color: var(--text);
}

[data-theme="light"] .nav-links a.active {
  color: var(--text);
}

.nav-actions {
  display: flex;
  align-items: center;
}

.btn.consultation {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: #fff;
  padding: 0.7rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn.consultation:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.toggle svg {
  display: none;
  fill: currentColor;
}

[data-theme="light"] .toggle .icon-sun {
  display: block;
}

[data-theme="dark"] .toggle .icon-moon {
  display: block;
}

.btn {
  --btn-bg: rgba(255, 255, 255, .06);
  --btn-bd: var(--glass-border);
  --btn-cl: var(--text);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: .5rem;
  padding: .7rem 1rem;
  border-radius: 12px;
  border: 1px solid var(--btn-bd);
  background: var(--btn-bg);
  color: var(--btn-cl);
  text-decoration: none;
  font-weight: 600;
  box-shadow: var(--shadow);
  transition: transform .15s ease, background .2s;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn.primary {
  --btn-bg: linear-gradient(135deg, var(--primary), var(--primary-2));
  --btn-bd: transparent;
  --btn-cl: #fff;
}

.btn.ghost {
  background: transparent;
  border: 1px solid var(--glass-border);
}

[data-theme="light"] .btn {
  --btn-bg: rgba(255, 255, 255, .9);
  --btn-bd: rgba(148, 163, 184, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

[data-theme="light"] .btn.ghost {
  background: transparent;
  border: 1px solid rgba(148, 163, 184, 0.3);
}

.btn.block {
  width: 100%;
}

main {
  padding-top: 18px;
}

/* Hero */
.hero {
  padding: 6rem 0 3rem;
  background-image: url('../header_Background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.hero-inner {
  position: relative;
  z-index: 2;
}

.hero-inner {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1.2fr 1fr;
  align-items: center;
}

.hero-copy h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  line-height: 1.1;
  margin: 0 0 1rem;
}

.hero-copy p {
  color: var(--muted);
  font-size: 1.1rem;
}

.cta {
  display: flex;
  gap: .75rem;
  margin: 1.25rem 0 1rem;
  flex-wrap: wrap;
}

.badges {
  display: flex;
  gap: .5rem;
  flex-wrap: wrap;
  padding: 0;
  margin: .5rem 0 0;
  list-style: none;
}

.badges li {
  padding: .35rem .6rem;
  border-radius: 999px;
  background: rgba(255, 255, 255, .06);
  color: var(--muted);
  border: 1px solid var(--glass-border);
  font-size: .9rem;
}

.glass {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.hero-card {
  padding: 1.25rem;
}

.hero-card .list {
  margin: .25rem 0 1rem;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: .75rem;
}

.metric {
  font-size: 1.8rem;
  font-weight: 800;
}

.label {
  color: var(--muted);
}

.hero-stats>div:nth-child(2) .metric {
  background: linear-gradient(135deg, var(--accent), var(--primary-2));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sections */
.section {
  padding: 4rem 0;
}

.section.alt {
  background: radial-gradient(800px 600px at 10% 20%, rgba(124, 92, 255, .18), transparent 60%),
    radial-gradient(600px 500px at 90% 10%, rgba(58, 166, 255, .18), transparent 60%);
}

[data-theme="light"] .section.alt {
  background: radial-gradient(800px 600px at 10% 20%, rgba(48, 243, 185, 0.08), transparent 60%),
    radial-gradient(600px 500px at 90% 10%, rgba(79, 70, 229, 0.12), transparent 60%);
}

.section-head {
  text-align: center;
  margin-bottom: 2rem;
}

.section-head p {
  color: var(--muted);
}

.grid {
  display: grid;
  gap: 1rem;
}

.cards-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* New Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.service-card {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 2rem 1.5rem;
  text-align: center;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius);
  padding: 1px;
  background: linear-gradient(135deg, var(--glass-border), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .service-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.service-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.service-card h3 {
  font-size: 1.3rem;
  margin: 0 0 1rem;
  color: var(--text);
}

.service-card p {
  color: var(--muted);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

/* Individual card background colors */
.service-card-1 {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.08),
      var(--glass));
}

.service-card-2 {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.08),
      var(--glass));
}

.service-card-3 {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.08),
      var(--glass));
}

.service-card-4 {
  background: linear-gradient(135deg,
      rgba(255, 107, 107, 0.08),
      var(--glass));
}

.service-card-5 {
  background: linear-gradient(135deg,
      rgba(255, 193, 7, 0.08),
      var(--glass));
}

.service-card-6 {
  background: linear-gradient(135deg,
      rgba(156, 39, 176, 0.08),
      var(--glass));
}

/* Light theme adjustments for service cards */
[data-theme="light"] .service-card-1 {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.05),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .service-card-2 {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.05),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .service-card-3 {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.05),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .service-card-4 {
  background: linear-gradient(135deg,
      rgba(255, 107, 107, 0.05),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .service-card-5 {
  background: linear-gradient(135deg,
      rgba(255, 193, 7, 0.05),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .service-card-6 {
  background: linear-gradient(135deg,
      rgba(156, 39, 176, 0.05),
      rgba(255, 255, 255, 0.95));
}

/* Features Grid Section */
.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.feature-card {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 2rem 1.5rem;
  text-align: center;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius);
  padding: 1px;
  background: linear-gradient(135deg, var(--glass-border), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .feature-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  color: var(--accent);
}

.feature-card h3 {
  font-size: 1.2rem;
  margin: 0 0 1rem;
  color: var(--text);
}

.feature-card p {
  color: var(--muted);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Individual feature card accents */
.feature-card:nth-child(1) {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.06),
      var(--glass));
}

.feature-card:nth-child(2) {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.06),
      var(--glass));
}

.feature-card:nth-child(3) {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.06),
      var(--glass));
}

.feature-card:nth-child(4) {
  background: linear-gradient(135deg,
      rgba(255, 107, 107, 0.06),
      var(--glass));
}

/* Light theme adjustments for feature cards */
[data-theme="light"] .feature-card:nth-child(1) {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .feature-card:nth-child(2) {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .feature-card:nth-child(3) {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .feature-card:nth-child(4) {
  background: linear-gradient(135deg,
      rgba(255, 107, 107, 0.04),
      rgba(255, 255, 255, 0.95));
}

.card {
  background: var(--card);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 1.25rem;
  box-shadow: var(--shadow);
}

.card.price {
  display: flex;
  flex-direction: column;
  gap: .75rem;
  align-items: stretch;
}

.card.price .chip {
  align-self: flex-start;
  padding: .2rem .55rem;
  border-radius: 999px;
  background: rgba(124, 92, 255, .2);
  border: 1px solid var(--glass-border);
  color: #fff;
  font-size: .8rem;
}

.card.price.featured {
  outline: 2px solid rgba(124, 92, 255, .55);
}

.price-tag {
  font-size: 2rem;
  font-weight: 800;
  margin: .25rem 0;
}

.price-tag span {
  opacity: .7;
  margin-right: .1rem;
}

.list {
  padding-left: 1.2rem;
}

.list.small li {
  margin: .25rem 0;
  color: var(--muted);
}

.list.ordered {
  list-style: decimal;
}

.cta-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
}

/* Contact */
.grid.contact {
  grid-template-columns: 1fr 1.1fr;
  align-items: start;
}

.contact-copy .info {
  margin-top: 1rem;
}

.form {
  padding: 1.25rem;
}

.field {
  display: grid;
  gap: .4rem;
  margin-bottom: .9rem;
}

.field label {
  font-weight: 600;
}

.field input,
.field select,
.field textarea {
  width: 100%;
  padding: .75rem .8rem;
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  background: rgba(255, 255, 255, .06);
  color: var(--text);
}

.field input::placeholder,
.field textarea::placeholder {
  color: color-mix(in oklab, var(--muted), #fff 20%);
  opacity: .7;
}

.error {
  color: #ff7b7b;
  min-height: 1em;
}

.form-status {
  color: var(--muted);
  margin-top: .5rem;
}

/* Footer */
.footer {
  border-top: 1px solid var(--glass-border);
  margin-top: 2rem;
}

.footer-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 0;
  color: var(--muted);
}

.footer-links {
  display: flex;
  gap: 1rem;
}

.footer a {
  color: var(--muted);
  text-decoration: none;
}

.footer a:hover {
  color: var(--text);
}

/* Utilities */
.narrow {
  width: min(800px, 100%);
  margin: 0 auto;
}

.cta-row {
  display: flex;
  gap: .75rem;
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 980px) {
  .hero-inner {
    grid-template-columns: 1fr;
  }

  .grid.contact {
    grid-template-columns: 1fr;
  }

  .cards-3 {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .services-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-card,
  .feature-card {
    padding: 1.5rem 1rem;
  }
}

/* FAQ Styles */
.faq-container {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  margin-bottom: 1rem;
  background: var(--card);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.faq-question {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
  background: transparent;
  border: none;
  color: var(--text);
  cursor: pointer;
  text-align: left;
  transition: background-color 0.2s ease;
}

.faq-question:hover {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="light"] .faq-question:hover {
  background: rgba(0, 0, 0, 0.02);
}

.faq-question h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.faq-icon {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--accent);
  transition: transform 0.3s ease;
  flex-shrink: 0;
  margin-left: 1rem;
}

.faq-question[aria-expanded="true"] .faq-icon {
  transform: rotate(45deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-question[aria-expanded="true"]+.faq-answer {
  max-height: 200px;
  padding: 0 1.25rem 1.25rem;
}

.faq-answer p {
  margin: 0;
  color: var(--muted);
  line-height: 1.6;
}

/* Accent color highlights */
.badges li:nth-child(2n) {
  background: rgba(48, 243, 185, 0.1);
  border-color: rgba(48, 243, 185, 0.3);
  color: var(--accent);
}

[data-theme="light"] .badges li:nth-child(2n) {
  background: rgba(48, 243, 185, 0.08);
  border-color: rgba(48, 243, 185, 0.2);
}

/* Improved light mode card styling */
[data-theme="light"] .card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

[data-theme="light"] .glass {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(148, 163, 184, 0.25);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* Skills Section */
.skills-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.skills-header {
  max-width: 500px;
}

.skills-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(48, 243, 185, 0.1);
  border: 1px solid rgba(48, 243, 185, 0.3);
  border-radius: 999px;
  color: var(--accent);
  font-size: 0.85rem;
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: 'Syne', Inter, system-ui, sans-serif;
}

.skills-header h2 {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  line-height: 1.2;
  margin: 0 0 1rem;
}

.skills-header p {
  color: var(--muted);
  line-height: 1.6;
  margin: 0;
}

.skills-bars {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text);
  font-family: 'Syne', Inter, system-ui, sans-serif;
}

.skill-percentage {
  font-weight: 700;
  color: var(--accent);
  font-size: 0.9rem;
}

.skill-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

[data-theme="light"] .skill-bar {
  background: rgba(0, 0, 0, 0.08);
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--accent), var(--primary-2));
  border-radius: 4px;
  width: 0%;
  transition: width 2s ease-in-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
 

/* Guarantee Section */
.guarantee-section {
  padding: 3rem 0;
}

.guarantee-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.guarantee-badge {
  display: inline-block;
  padding: 0.5rem 2rem;
  background: transparent;
  border: none;
  color: #fff;
  font-weight: 700;
  font-size: 1rem;
  margin-bottom: 1rem;
  font-family: 'Syne', Inter, system-ui, sans-serif;
}

@keyframes neonGlow {
  from {
    box-shadow: 0 0 20px rgba(48, 243, 185, 0.4), 0 0 40px rgba(48, 243, 185, 0.2);
  }

  to {
    box-shadow: 0 0 30px rgba(48, 243, 185, 0.6), 0 0 60px rgba(48, 243, 185, 0.3);
  }
}

.guarantee-content h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  line-height: 1.1;
  margin: 0 0 1.5rem;
  background: linear-gradient(135deg, var(--text), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.guarantee-content p {
  color: var(--muted);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem;
}

.guarantee-content h2 {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  line-height: 1.2;
  margin: 0 0 1rem;
  color: var(--text);
}

.guarantee-content p {
  color: rgba(255, 255, 255, 0.85);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem;
}

.guarantee-features {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}

.guarantee-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 999px;
  font-weight: 600;
  color: #fff;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-size: 0.9rem;
}

.guarantee-feature:nth-child(1) {
  background: linear-gradient(135deg, var(--accent), var(--primary-2));
}

.guarantee-feature:nth-child(2) {
  background: linear-gradient(135deg, var(--primary), var(--accent));
}

.guarantee-feature:nth-child(3) {
  background: linear-gradient(135deg, var(--primary-2), var(--primary));
}

.feature-icon {
  font-size: 1rem;
}

.guarantee-btn {
  background: linear-gradient(135deg, var(--accent), var(--primary-2));
  border: none;
  color: #fff;
  font-size: 1rem;
  padding: 0.8rem 1.5rem;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.guarantee-btn:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 980px) {
  .skills-section {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .guarantee-features {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

/* Futuristic particle animation */
@keyframes floaty {
  from {
    transform: translateY(0px);
  }

  to {
    transform: translateY(-6px);
  }
}

/* B
enefits Grid Section */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-top: 2rem;
}

.benefit-card {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 2rem 1.5rem;
  text-align: center;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.benefit-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: var(--radius);
  padding: 1px;
  background: linear-gradient(135deg, var(--glass-border), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .benefit-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  color: var(--accent);
}

.benefit-card h3 {
  font-size: 1.2rem;
  margin: 0 0 1rem;
  color: var(--text);
}

.benefit-card p {
  color: var(--muted);
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Individual benefit card accents */
.benefit-card:nth-child(1) {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.06),
      var(--glass));
}

.benefit-card:nth-child(2) {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.06),
      var(--glass));
}

.benefit-card:nth-child(3) {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.06),
      var(--glass));
}

.benefit-card:nth-child(4) {
  background: linear-gradient(135deg,
      rgba(255, 193, 7, 0.06),
      var(--glass));
}

/* Light theme adjustments for benefit cards */
[data-theme="light"] .benefit-card:nth-child(1) {
  background: linear-gradient(135deg,
      rgba(48, 243, 185, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .benefit-card:nth-child(2) {
  background: linear-gradient(135deg,
      rgba(58, 166, 255, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .benefit-card:nth-child(3) {
  background: linear-gradient(135deg,
      rgba(124, 92, 255, 0.04),
      rgba(255, 255, 255, 0.95));
}

[data-theme="light"] .benefit-card:nth-child(4) {
  background: linear-gradient(135deg,
      rgba(255, 193, 7, 0.04),
      rgba(255, 255, 255, 0.95));
}

/* Placeholder sections styling */
.portfolio-placeholder,
.testimonials-placeholder {
  text-align: center;
  padding: 3rem 0;
  color: var(--muted);
  font-style: italic;
}

/* R
esponsive adjustments for benefits */
@media (max-width: 980px) {
  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 640px) {
  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .benefit-card {
    padding: 1.5rem 1rem;
  }
}

/* FAQ 
Styles - New Design */
.faq-container {
  max-width: 900px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  margin-bottom: 1rem;
  background: var(--card);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.faq-question {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: transparent;
  border: none;
  color: var(--text);
  cursor: pointer;
  text-align: left;
  transition: background-color 0.2s ease;
}

.faq-question:hover {
  background: rgba(255, 255, 255, 0.05);
}

[data-theme="light"] .faq-question:hover {
  background: rgba(0, 0, 0, 0.02);
}

.faq-question h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .faq-question h3 {
  color: var(--text);
}

.faq-icon {
  font-size: 1.5rem;
  font-weight: 300;
  color: var(--accent);
  transition: transform 0.3s ease;
  flex-shrink: 0;
  margin-left: 1rem;
}

.faq-question[aria-expanded="true"] .faq-icon {
  transform: rotate(45deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-question[aria-expanded="true"]+.faq-answer {
  max-height: 300px;
  padding: 0 1.5rem 1.5rem;
}

.faq-answer p {
  margin: 0;
  color: rgba(255, 255, 255, 0.75);
  line-height: 1.6;
  font-size: 0.95rem;
}

[data-theme="light"] .faq-answer p {
  color: var(--muted);
}

/* Navba
r responsive design */
@media (max-width: 980px) {
  .nav-links {
    gap: 1.5rem;
  }

  .nav-links a {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .nav-inner {
    padding: 0.8rem 0;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-links a {
    font-size: 0.85rem;
  }

  .btn.consultation {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 640px) {
  .nav-links {
    display: none;
  }

  .brand {
    font-size: 1rem;
  }
}

/* Consult
ation Section */
.consultation-section {
  background: rgba(0, 0, 0, 0.9);
  padding: 4rem 0;
}

.consultation-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.consultation-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  letter-spacing: 1px;
}

.consultation-content h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
  margin: 0 0 1.5rem;
  color: #fff;
  font-weight: 400;
}

.consultation-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.btn.consultation-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: #fff;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
  text-transform: none;
}

.btn.consultation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
}

/* Rent Website Service Section */
.rent-service-section {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  position: relative;
  overflow: hidden;
}

.rent-service-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(48, 243, 185, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(58, 166, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.rent-service-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.rent-service-left h2 {
  font-size: clamp(2rem, 4vw, 3.5rem);
  line-height: 1.1;
  margin: 0;
  color: var(--accent);
  font-weight: 600;
}

.rent-service-right {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius);
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.rent-service-info h3 {
  color: #fff;
  font-size: 1.5rem;
  margin: 0 0 1.5rem;
  font-weight: 600;
}

.rent-features {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem;
}

.rent-features li {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  position: relative;
  font-weight: 500;
}

.rent-features li::before {
  content: '•';
  color: var(--accent);
  font-size: 1.2rem;
  position: absolute;
  left: 0;
  top: 0;
}

.rent-tagline {
  color: #fff;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 1.5rem 0;
  text-align: center;
}

.btn.rent-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #fff;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  display: block;
  text-align: center;
  margin: 0 auto;
  width: fit-content;
}

.btn.rent-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Responsive design for new sections */
@media (max-width: 980px) {
  .rent-service-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .consultation-content h2 {
    font-size: clamp(2rem, 6vw, 3rem);
  }
}

@media (max-width: 640px) {
  .consultation-section {
    padding: 3rem 0;
  }

  .rent-service-right {
    padding: 1.5rem;
  }

  .rent-service-left h2 {
    font-size: clamp(1.8rem, 5vw, 2.5rem);
  }
}

/* Un
iversal Section Background (except hero, navbar, footer) */
.section:not(.hero):not(.navbar):not(.footer) {
  background: rgba(0, 0, 0, 0.9);
  position: relative;
}

.section:not(.hero):not(.navbar):not(.footer)::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(48, 243, 185, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  z-index: 1;
}

.section:not(.hero):not(.navbar):not(.footer) .container {
  position: relative;
  z-index: 2;
}

/* Enhanced Consultation Section */
.consultation-section {
  background: rgba(0, 0, 0, 0.95) !important;
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
}

.consultation-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(48, 243, 185, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.consultation-content {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem 2rem;
  backdrop-filter: blur(10px);
}

.consultation-badge {
  display: inline-block;
  padding: 0.6rem 1.5rem;
  background: linear-gradient(135deg, var(--accent), var(--primary-2));
  color: #fff;
  font-size: 0.9rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  letter-spacing: 1px;
  border-radius: 999px;
  box-shadow: 0 4px 15px rgba(48, 243, 185, 0.3);
}

.consultation-content h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
  margin: 0 0 1.5rem;
  color: #fff;
  font-weight: 600;
  background: linear-gradient(135deg, #fff, var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.consultation-content p {
  color: rgba(255, 255, 255, 0.85);
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.consultation-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.consultation-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 999px;
  font-weight: 600;
  color: #fff;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-size: 0.9rem;
  backdrop-filter: blur(5px);
}

.consultation-icon {
  font-size: 1.2rem;
}

.btn.consultation-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: #fff;
  padding: 1.2rem 2.5rem;
  border-radius: 12px;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
  text-transform: none;
  margin-top: 1rem;
}

.btn.consultation-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.5);
}

/* Responsive design for consultation */
@media (max-width: 980px) {
  .consultation-features {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .consultation-content {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 640px) {
  .consultation-section {
    padding: 3rem 0;
  }

  .consultation-content {
    padding: 2rem 1rem;
  }
}

/* Updated C
onsultation Section - Smaller and Full Width */
.consultation-section {
  background: rgba(0, 0, 0, 0.95);
  padding: 2rem 0;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.consultation-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(48, 243, 185, 0.08) 0%, transparent 50%);
  z-index: 1;
}

.consultation-content {
  text-align: center;
  width: 100%;
  max-width: none;
  margin: 0;
  position: relative;
  z-index: 2;
  padding: 2rem;
}

.consultation-badge {
  display: inline-block;
  padding: 0.4rem 1rem;
  background: linear-gradient(135deg, var(--accent), var(--primary-2));
  color: #fff;
  font-size: 0.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  letter-spacing: 1px;
  border-radius: 999px;
  box-shadow: 0 2px 10px rgba(48, 243, 185, 0.3);
}

.consultation-content h2 {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  line-height: 1.1;
  margin: 0 0 1rem;
  color: #fff;
  font-weight: 600;
}

.consultation-content p {
  color: rgba(255, 255, 255, 0.85);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.consultation-features {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 1.5rem 0;
  flex-wrap: wrap;
}

.consultation-feature {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 999px;
  font-weight: 600;
  color: #fff;
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-size: 0.8rem;
}

.consultation-icon {
  font-size: 1rem;
}

.btn.consultation-btn {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: #fff;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  text-transform: none;
  margin-top: 1rem;
}

.btn.consultation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

/* Remove glass effects and add glowing edges to service cards */
.glass {
  background: var(--card);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
}

.service-card {
  background: var(--glass);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 2rem 1.5rem;
  text-align: center;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: var(--radius);
  padding: 2px;
  background: linear-gradient(45deg, transparent, var(--accent), transparent, var(--primary), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 0.6;
  animation: glow-rotate 2s linear infinite;
}

@keyframes glow-rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .service-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 1200px) {
  .container {
    width: min(1200px, calc(100% - 1.5rem));
  }
}

@media (max-width: 980px) {
  .hero-inner {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .grid.contact {
    grid-template-columns: 1fr;
  }

  .cards-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .benefits-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .skills-section {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .rent-service-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .consultation-features {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .consultation-content {
    padding: 1.5rem;
  }

  .nav-links {
    gap: 1.5rem;
  }

  .nav-links a {
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .cards-3 {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-card,
  .feature-card,
  .benefit-card {
    padding: 1.5rem 1rem;
  }

  .hero {
    padding: 4rem 0 2rem;
  }

  .section {
    padding: 3rem 0;
  }

  .consultation-section {
    padding: 1.5rem 0;
  }

  .nav-inner {
    padding: 0.8rem 0;
  }

  .nav-links {
    gap: 1rem;
  }

  .nav-links a {
    font-size: 0.85rem;
  }

  .btn.consultation {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 640px) {
  .container {
    width: calc(100% - 1rem);
  }

  .nav-links {
    display: none;
  }

  .brand {
    font-size: 1rem;
  }

  .hero-copy h1 {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
  }

  .consultation-content {
    padding: 1rem;
  }

  .consultation-features {
    gap: 0.5rem;
  }

  .consultation-feature {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  .rent-service-right {
    padding: 1.5rem;
  }

  .rent-service-left h2 {
    font-size: clamp(1.8rem, 5vw, 2.5rem);
  }
}

/* Remove backdrop-filter and glass effects */
.hero-card,
.faq-item,
.benefit-card,
.feature-card {
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
}

/* Ensure all pages are mobile responsive */
.form {
  padding: 1.25rem;
}

@media (max-width: 768px) {
  .form {
    padding: 1rem;
  }

  .field input,
  .field select,
  .field textarea {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 640px) {
  .form {
    padding: 0.8rem;
  }
}

/* 
Header Logo Styling */
.nav-logo {
  display: flex;
  align-items: center;
}

.header-logo {
  height: 50px;
  width: auto;
  transition: transform 0.3s ease;
}

.header-logo:hover {
  transform: scale(1.05);
}

/* Neon Animated Heading */
.neon-heading {
  font-size: clamp(2.5rem, 6vw, 4rem) !important;
  font-weight: 800 !important;
  text-align: center;
  color: #fff;
  text-shadow:
    0 0 5px var(--accent),
    0 0 10px var(--accent),
    0 0 15px var(--accent),
    0 0 20px var(--accent),
    0 0 35px var(--accent),
    0 0 40px var(--accent);
  animation: neonGlow 2s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite;
  letter-spacing: 2px;
  line-height: 1.1;
  margin-bottom: 2rem !important;
}

@keyframes neonGlow {
  from {
    text-shadow:
      0 0 5px var(--accent),
      0 0 10px var(--accent),
      0 0 15px var(--accent),
      0 0 20px var(--accent),
      0 0 35px var(--accent),
      0 0 40px var(--accent);
  }

  to {
    text-shadow:
      0 0 2px var(--accent),
      0 0 5px var(--accent),
      0 0 8px var(--accent),
      0 0 12px var(--accent),
      0 0 18px var(--accent),
      0 0 25px var(--accent);
  }
}

@keyframes textFloat {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* Additional neon pulse effect */
.neon-heading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, var(--accent), transparent);
  opacity: 0;
  animation: neonSweep 3s linear infinite;
  z-index: -1;
}

@keyframes neonSweep {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  50% {
    opacity: 0.1;
  }

  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Update hero copy for better centering */
.hero-copy {
  text-align: center;
}

.hero-copy p {
  max-width: 600px;
  margin: 0 auto 2rem;
}

/* Responsive adjustments for header logo */
@media (max-width: 768px) {
  .header-logo {
    height: 40px;
  }

  .neon-heading {
    font-size: clamp(2rem, 8vw, 3rem) !important;
    letter-spacing: 1px;
  }
}

@media (max-width: 640px) {
  .header-logo {
    height: 35px;
  }

  .nav-inner {
    justify-content: space-between;
  }

  .brand {
    flex: 1;
  }

  .nav-logo {
    flex-shrink: 0;
  }
}

* Updated Neon Animated Heading with Tech Font */ .neon-heading {
  font-family: 'Orbitron', 'Rajdhani', 'DM Sans', monospace !important;
  font-size: clamp(2.5rem, 6vw, 4rem) !important;
  font-weight: 900 !important;
  text-align: center;
  color: #fff;
  text-shadow:
    0 0 5px var(--accent),
    0 0 10px var(--accent),
    0 0 15px var(--accent),
    0 0 20px var(--accent),
    0 0 35px var(--accent),
    0 0 40px var(--accent);
  animation: neonGlow 2s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite;
  letter-spacing: 3px;
  line-height: 1.1;
  margin-bottom: 2rem !important;
  text-transform: uppercase;
}

@keyframes neonGlow {
  from {
    text-shadow:
      0 0 5px var(--accent),
      0 0 10px var(--accent),
      0 0 15px var(--accent),
      0 0 20px var(--accent),
      0 0 35px var(--accent),
      0 0 40px var(--accent);
  }

  to {
    text-shadow:
      0 0 2px var(--accent),
      0 0 5px var(--accent),
      0 0 8px var(--accent),
      0 0 12px var(--accent),
      0 0 18px var(--accent),
      0 0 25px var(--accent);
  }
}

@keyframes textFloat {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* Hero Logo Styling */
.hero-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hero-logo-img {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  filter: drop-shadow(0 0 20px rgba(48, 243, 185, 0.3));
  animation: logoFloat 4s ease-in-out infinite;
}

@keyframes logoFloat {

  0%,
  100% {
    transform: translateY(0px) scale(1);
  }

  50% {
    transform: translateY(-15px) scale(1.05);
  }
}

/* Testimonials Section */
.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.testimonial-card {
  background: var(--card);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .testimonial-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.testimonial-stars {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.star {
  color: #ffd700;
  font-size: 1.2rem;
}

.testimonial-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: italic;
}

[data-theme="light"] .testimonial-text {
  color: var(--muted);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--primary-2));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  border: 2px solid var(--accent);
}

.author-info h4 {
  margin: 0;
  color: var(--text);
  font-size: 1rem;
  font-weight: 600;
}

.author-role {
  color: var(--accent);
  font-size: 0.85rem;
  font-weight: 500;
}

/* Update hero layout for better balance */
.hero-inner {
  display: grid;
  gap: 3rem;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}

.hero-copy {
  text-align: left;
}

.hero-copy p {
  max-width: none;
  margin: 0 0 2rem;
}

/* Responsive adjustments */
@media (max-width: 980px) {
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .hero-inner {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-copy {
    text-align: center;
  }

  .hero-copy p {
    max-width: 600px;
    margin: 0 auto 2rem;
  }

  .hero-logo-img {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .neon-heading {
    font-size: clamp(2rem, 8vw, 3rem) !important;
    letter-spacing: 2px;
  }

  .hero-logo-img {
    max-height: 250px;
  }

  .testimonial-card {
    padding: 1.5rem;
  }
}

@media (max-width: 640px) {
  .hero-logo-img {
    max-height: 200px;
  }

  .neon-heading {
    letter-spacing: 1px;
  }
}
* Updated Heading with RQND Pro Font and Soft Glow */
.neon-heading {
  font-family: 'RQND Pro', 'DM Sans', sans-serif !important;
  font-size: clamp(3rem, 7vw, 5rem) !important;
  font-weight: 700 !important;
  text-align: center;
  color: #fff;
  text-shadow: 
    0 0 10px rgba(48, 243, 185, 0.6),
    0 0 20px rgba(48, 243, 185, 0.4),
    0 0 30px rgba(48, 243, 185, 0.3);
  animation: softGlow 3s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite;
  letter-spacing: 2px;
  line-height: 1.1;
  margin-bottom: 2rem !important;
  text-transform: uppercase;
}

@keyframes softGlow {
  from {
    text-shadow: 
      0 0 10px rgba(48, 243, 185, 0.6),
      0 0 20px rgba(48, 243, 185, 0.4),
      0 0 30px rgba(48, 243, 185, 0.3);
  }
  to {
    text-shadow: 
      0 0 15px rgba(48, 243, 185, 0.8),
      0 0 25px rgba(48, 243, 185, 0.6),
      0 0 35px rgba(48, 243, 185, 0.4);
  }
}

/* Portfolio Section Styling */
.portfolio-section {
  background: rgba(0, 0, 0, 0.95);
  padding: 4rem 0;
}

.portfolio-header {
  text-align: left;
  margin-bottom: 3rem;
}

.portfolio-title {
  font-family: 'DM Sans', sans-serif;
  font-size: clamp(3rem, 6vw, 4.5rem);
  font-weight: 800;
  color: #fff;
  margin: 0 0 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.portfolio-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.portfolio-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: var(--accent);
}

.portfolio-image {
  height: 250px;
  background: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.portfolio-placeholder {
  color: #999;
  font-size: 1.2rem;
  font-weight: 500;
}

.portfolio-info {
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.8);
}

.portfolio-category {
  color: var(--accent);
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

.portfolio-project-title {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.3;
}

.portfolio-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: var(--accent);
  box-shadow: 0 0 10px rgba(48, 243, 185, 0.5);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.6);
}

/* Portfolio Item Hover Effects */
.portfolio-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(48, 243, 185, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.portfolio-item:hover::before {
  opacity: 1;
}

/* Responsive Design for Portfolio */
@media (max-width: 1200px) {
  .portfolio-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 980px) {
  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .portfolio-header {
    text-align: center;
  }
  
  .portfolio-subtitle {
    margin: 0 auto;
  }
}

@media (max-width: 640px) {
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .portfolio-image {
    height: 200px;
  }
  
  .portfolio-info {
    padding: 1rem;
  }
  
  .neon-heading {
    font-size: clamp(2.5rem, 8vw, 4rem) !important;
  }
}

/* Add click interaction for portfolio items */
.portfolio-item:active {
  transform: translateY(-5px) scale(0.98);
}/* RQN
D Pro Font Heading - Override all previous styles */
h1.neon-heading {
  font-family: 'RQND Pro', 'DM Sans', sans-serif !important;
  font-size: clamp(3rem, 7vw, 5rem) !important;
  font-weight: 700 !important;
  text-align: center !important;
  color: #fff !important;
  text-shadow: 
    0 0 10px rgba(48, 243, 185, 0.6),
    0 0 20px rgba(48, 243, 185, 0.4),
    0 0 30px rgba(48, 243, 185, 0.3) !important;
  animation: softGlow 3s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite !important;
  letter-spacing: 2px !important;
  line-height: 1.1 !important;
  margin-bottom: 2rem !important;
  text-transform: uppercase !important;
}

@keyframes softGlow {
  from {
    text-shadow: 
      0 0 10px rgba(48, 243, 185, 0.6),
      0 0 20px rgba(48, 243, 185, 0.4),
      0 0 30px rgba(48, 243, 185, 0.3);
  }
  to {
    text-shadow: 
      0 0 15px rgba(48, 243, 185, 0.8),
      0 0 25px rgba(48, 243, 185, 0.6),
      0 0 35px rgba(48, 243, 185, 0.4);
  }
}

@keyframes textFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}/* R
QND Pro Heading Animations - FIXED */
@keyframes softGlow {
  from {
    text-shadow: 
      0 0 10px rgba(48, 243, 185, 0.6),
      0 0 20px rgba(48, 243, 185, 0.4),
      0 0 30px rgba(48, 243, 185, 0.3);
  }
  to {
    text-shadow: 
      0 0 15px rgba(48, 243, 185, 0.8),
      0 0 25px rgba(48, 243, 185, 0.6),
      0 0 35px rgba(48, 243, 185, 0.4);
  }
}

@keyframes textFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* FORCE RQND Pro Font and Effects - Ultimate Override */
.neon-heading,
h1.neon-heading,
.hero-copy .neon-heading,
.hero-copy h1.neon-heading {
  font-family: 'RQND Pro', 'DM Sans', sans-serif !important;
  font-size: clamp(3rem, 7vw, 5rem) !important;
  font-weight: 700 !important;
  text-align: center !important;
  color: #fff !important;
  text-shadow: 
    0 0 10px rgba(48, 243, 185, 0.6),
    0 0 20px rgba(48, 243, 185, 0.4),
    0 0 30px rgba(48, 243, 185, 0.3) !important;
  animation: softGlow 3s ease-in-out infinite alternate, textFloat 3s ease-in-out infinite !important;
  letter-spacing: 2px !important;
  line-height: 1.1 !important;
  margin-bottom: 2rem !important;
  text-transform: uppercase !important;
}

/* Remove Glass Effects - Use Solid Background Color */
.hero-card,
.card,
.service-card,
.benefit-card,
.feature-card,
.testimonial-card,
.portfolio-item,
.faq-item,
.consultation-content,
.rent-service-info {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
}

/* Specific Card Backgrounds */
.hero-card {
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

.service-card,
.benefit-card,
.feature-card {
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.testimonial-card {
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
}

.portfolio-item {
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.faq-item {
  background: #0E1B38 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Navbar - Solid Background for All Pages */
.navbar {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background: rgba(0, 0, 0, 0.95) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Glass Effect Only for Contact Page Navbar */
body.contact-page .navbar {
  backdrop-filter: blur(25px) saturate(200%) !important;
  -webkit-backdrop-filter: blur(25px) saturate(200%) !important;
  background: rgba(0, 0, 0, 0.8) !important;
}

/* Form Glass Effects */
.form {
  backdrop-filter: blur(15px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(15px) saturate(180%) !important;
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.field input, 
.field select, 
.field textarea {
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Consultation Section Glass */
.consultation-content {
  backdrop-filter: blur(20px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(20px) saturate(180%) !important;
  background: rgba(255, 255, 255, 0.06) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

.consultation-feature {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Rent Service Glass */
.rent-service-info {
  backdrop-filter: blur(15px) saturate(160%) !important;
  -webkit-backdrop-filter: blur(15px) saturate(160%) !important;
  background: rgba(255, 255, 255, 0.07) !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
}

/* Guarantee Features Glass */
.guarantee-feature {
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  background: rgba(255, 255, 255, 0.06) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Enhanced Hover Effects with Solid Background */
.service-card:hover,
.benefit-card:hover,
.feature-card:hover,
.testimonial-card:hover,
.portfolio-item:hover {
  background: #1a2951 !important;
  border: 1px solid rgba(48, 243, 185, 0.4) !important;
  transform: translateY(-5px) !important;
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(48, 243, 185, 0.2) !important;
}/* Glass Ef
fects ONLY for Contact Page */
body.contact-page .glass,
body.contact-page .form,
body.contact-page .card {
  backdrop-filter: blur(15px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(15px) saturate(180%) !important;
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Contact Page Form Glass Effects */
.form {
  backdrop-filter: blur(15px) saturate(180%) !important;
  -webkit-backdrop-filter: blur(15px) saturate(180%) !important;
  background: rgba(255, 255, 255, 0.08) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.field input, 
.field select, 
.field textarea {
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #0a0c10;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--accent), var(--primary));
  border-radius: 6px;
  border: 2px solid #0a0c10;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary), var(--accent));
}

/* Firefox Scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: var(--accent) #0a0c10;
}

/* Fix Gap Between Header and About Us Section */
.hero {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.section {
  margin-top: 0 !important;
  padding-top: 4rem !important;
}

.section:first-of-type {
  padding-top: 2rem !important;
}

/* Ensure continuous background */
main {
  background: inherit;
  margin: 0;
  padding: 0;
}

/* Remove any gaps between sections */
.section + .section {
  margin-top: 0 !important;
}

/* Consultation Section Updates */
.consultation-content {
  background: #0E1B38 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.consultation-feature {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Rent Service Updates */
.rent-service-info {
  background: #0E1B38 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Guarantee Features Updates */
.guarantee-feature {
  background: #0E1B38 !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}
/* Enha
nced Portfolio Modal Styles */
.portfolio-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  background: var(--bg-2);
  margin: 2% auto;
  padding: 0;
  border: 1px solid var(--glass-border);
  border-radius: var(--radius);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow);
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 30px;
  color: var(--muted);
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: var(--text);
}

.modal-header {
  padding: 30px;
  border-bottom: 1px solid var(--glass-border);
}

.modal-title {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text);
}

.modal-description {
  margin: 0;
  color: var(--muted);
  font-size: 1.1rem;
  line-height: 1.6;
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--glass-border);
  background: var(--bg);
}

.modal-tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  color: var(--muted);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.modal-tab:hover {
  color: var(--text);
  background: var(--glass);
}

.modal-tab.active {
  color: var(--primary);
  background: var(--glass);
}

.modal-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary);
}

.modal-tab-contents {
  padding: 30px;
}

.modal-tab-content {
  display: none;
}

.modal-tab-content.active {
  display: block;
}

.modal-media-item {
  margin-bottom: 20px;
  border-radius: var(--radius);
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: var(--card);
}

.modal-media-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-image, .modal-video {
  width: 100%;
  height: auto;
  display: block;
  border-radius: var(--radius);
}

.modal-video {
  max-height: 400px;
  object-fit: cover;
}

/* Portfolio Grid Updates */
.portfolio-img, .portfolio-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius);
  transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-img,
.portfolio-item:hover .portfolio-video {
  transform: scale(1.05);
}

.portfolio-video {
  max-height: 300px;
}

/* Media Overlay for Expanded View */
.media-overlay {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.overlay-close {
  position: absolute;
  top: 30px;
  right: 40px;
  color: var(--text);
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  z-index: 2001;
  transition: color 0.3s ease;
}

.overlay-close:hover {
  color: var(--primary);
}

.overlay-media {
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--radius);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.overlay-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: var(--radius);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 5% auto;
    max-height: 85vh;
  }
  
  .modal-header {
    padding: 20px;
  }
  
  .modal-title {
    font-size: 1.5rem;
  }
  
  .modal-tabs {
    flex-direction: column;
  }
  
  .modal-tab {
    text-align: center;
    padding: 12px;
  }
  
  .modal-tab-contents {
    padding: 20px;
  }
  
  .overlay-close {
    top: 20px;
    right: 25px;
    font-size: 30px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    margin: 2% auto;
  }
  
  .modal-header {
    padding: 15px;
  }
  
  .modal-tab-contents {
    padding: 15px;
  }
  
  .close-modal {
    top: 15px;
    right: 20px;
    font-size: 24px;
  }
}/*
 Portfolio Section Styles */
.portfolio-section {
  padding: 80px 0;
}

.portfolio-header {
  text-align: center;
  margin-bottom: 60px;
}

.portfolio-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text);
}

.portfolio-subtitle {
  font-size: 1.2rem;
  color: var(--muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.portfolio-item {
  background: var(--card);
  border-radius: var(--radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--glass-border);
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  border-color: var(--primary);
}

.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.portfolio-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--bg);
  color: var(--muted);
  font-size: 1.2rem;
  font-weight: 500;
}

.portfolio-info {
  padding: 25px;
}

.portfolio-category {
  color: var(--primary);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.portfolio-project-title {
  margin: 10px 0 0 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text);
  line-height: 1.3;
}

.portfolio-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 40px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--muted);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.5;
}

.dot.active,
.dot:hover {
  background: var(--primary);
  opacity: 1;
  transform: scale(1.2);
}

/* Responsive Portfolio Grid */
@media (max-width: 1200px) {
  .portfolio-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .portfolio-section {
    padding: 60px 0;
  }
  
  .portfolio-title {
    font-size: 2.5rem;
  }
  
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .portfolio-image {
    height: 200px;
  }
  
  .portfolio-info {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .portfolio-title {
    font-size: 2rem;
  }
  
  .portfolio-subtitle {
    font-size: 1rem;
  }
  
  .portfolio-info {
    padding: 15px;
  }
  
  .portfolio-project-title {
    font-size: 1.2rem;
  }
}/* No
 content message */
.no-content {
  text-align: center;
  color: var(--muted);
  font-style: italic;
  padding: 40px 20px;
  background: var(--bg);
  border-radius: var(--radius);
  border: 1px dashed var(--glass-border);
}/* 
HK Modular Hero Heading with Gradient */
.neon-heading {
  font-family: 'HK Modular', 'Orbitron', 'DM Sans', sans-serif !important;
  font-size: clamp(3rem, 7vw, 5rem) !important;
  font-weight: 700 !important;
  text-align: center !important;
  background: linear-gradient(135deg, #ffffff 0%, #30F3B9 25%, #7c5cff 50%, #3aa6ff 75%, #ffffff 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease-in-out infinite, textFloat 3s ease-in-out infinite !important;
  letter-spacing: 2px !important;
  line-height: 1.1 !important;
  margin-bottom: 2rem !important;
  text-transform: uppercase !important;
  text-shadow: none !important;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes textFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

@keyframes softGlow {
  0%, 100% { filter: drop-shadow(0 0 10px rgba(48, 243, 185, 0.3)); }
  50% { filter: drop-shadow(0 0 20px rgba(124, 92, 255, 0.4)); }
}

/* Add subtle glow effect */
.neon-heading {
  filter: drop-shadow(0 0 15px rgba(48, 243, 185, 0.2));
  animation: gradientShift 4s ease-in-out infinite, textFloat 3s ease-in-out infinite, softGlow 3s ease-in-out infinite !important;
}/* 
WhatsApp Link Styling */
.whatsapp-link {
  color: #25D366 !important;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.whatsapp-link:hover {
  color: #128C7E !important;
  transform: translateY(-1px);
}

.whatsapp-link::before {
  content: "💬";
  font-size: 1rem;
}

/* Light theme WhatsApp link */
[data-theme="light"] .whatsapp-link {
  color: #25D366 !important;
}

[data-theme="light"] .whatsapp-link:hover {
  color: #128C7E !important;
}/* E
nhanced WhatsApp Link Styling */
.whatsapp-link {
  color: #25D366 !important;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  background: rgba(37, 211, 102, 0.1);
  border: 1px solid rgba(37, 211, 102, 0.3);
  font-size: 0.95rem;
}

.whatsapp-link:hover {
  color: #128C7E !important;
  transform: translateY(-1px);
  background: rgba(37, 211, 102, 0.15);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.2);
}

.whatsapp-link::before {
  content: "💬";
  font-size: 1rem;
}

/* Light theme WhatsApp link */
[data-theme="light"] .whatsapp-link {
  color: #25D366 !important;
  background: rgba(37, 211, 102, 0.08);
  border: 1px solid rgba(37, 211, 102, 0.2);
}

[data-theme="light"] .whatsapp-link:hover {
  color: #128C7E !important;
  background: rgba(37, 211, 102, 0.12);
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.15);
}/* Re
move existing service card animations and add glowing border effect */
.service-card {
  position: relative;
  overflow: visible !important;
}

.service-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: var(--radius);
  padding: 2px;
  background: linear-gradient(45deg, transparent, transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover::before {
  opacity: 1;
  background: linear-gradient(
    45deg,
    var(--accent) 0%,
    var(--primary) 25%,
    var(--primary-2) 50%,
    var(--accent) 75%,
    var(--primary) 100%
  );
  background-size: 300% 300%;
  animation: glowingBorder 2s linear infinite;
}

@keyframes glowingBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 
              0 0 20px rgba(48, 243, 185, 0.1);
}

[data-theme="light"] .service-card:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1),
              0 0 20px rgba(48, 243, 185, 0.08);
}

/* Enhanced Contact Page Styling - Match Rent Service Style */
.contact-copy {
  position: relative;
}

.contact-copy h1 {
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--text) 0%, var(--accent) 50%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: none;
}

.contact-copy p {
  font-family: 'Syne', Inter, system-ui, sans-serif;
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--muted);
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 500px;
}

/* Add subtle animation to contact text */
.contact-copy h1 {
  animation: subtleFloat 4s ease-in-out infinite;
}

@keyframes subtleFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

/* Responsive adjustments for contact */
@media (max-width: 768px) {
  .contact-copy h1 {
    font-size: clamp(2rem, 6vw, 3rem);
    text-align: center;
  }
  
  .contact-copy p {
    text-align: center;
    margin: 0 auto 2rem;
  }
}/* 
Override any existing service card animations */
.service-card * {
  animation: none !important;
}

.service-card::after {
  display: none !important;
}

/* Enhanced glowing border effect with better visibility */
.service-card:hover::before {
  opacity: 1;
  background: linear-gradient(
    45deg,
    rgba(48, 243, 185, 0.8) 0%,
    rgba(124, 92, 255, 0.8) 25%,
    rgba(58, 166, 255, 0.8) 50%,
    rgba(48, 243, 185, 0.8) 75%,
    rgba(124, 92, 255, 0.8) 100%
  );
  background-size: 400% 400%;
  animation: glowingBorder 3s ease-in-out infinite;
  filter: blur(1px);
}

/* Add inner glow effect */
.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.2), 
    inset 0 0 20px rgba(48, 243, 185, 0.05),
    0 0 30px rgba(48, 243, 185, 0.1);
}

[data-theme="light"] .service-card:hover {
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.1),
    inset 0 0 20px rgba(48, 243, 185, 0.03),
    0 0 30px rgba(48, 243, 185, 0.08);
}

/* Ensure smooth transitions */
.service-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card::before {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}