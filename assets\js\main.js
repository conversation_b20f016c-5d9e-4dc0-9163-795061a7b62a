(function () {
    // Loading Screen
    window.addEventListener('load', function () {
        const loadingScreen = document.getElementById('loading-screen');
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.visibility = 'hidden';
            document.body.classList.add('loaded');
        }, 2000); // Show loading screen for 2 seconds
    });

    // Cursor Follower
    const cursorFollower = document.querySelector('.cursor-follower');
    if (cursorFollower) {
        document.addEventListener('mousemove', (e) => {
            cursorFollower.style.opacity = '1';
            cursorFollower.style.left = e.clientX + 'px';
            cursorFollower.style.top = e.clientY + 'px';
            cursorFollower.style.zIndex = '0';
        });

        document.addEventListener('mouseleave', () => {
            cursorFollower.style.opacity = '0';
        });
    }

    // Theme Handling
    function getStoredTheme() {
        try { return localStorage.getItem('theme') || 'dark'; } catch { return 'dark'; }
    }
    function storeTheme(theme) {
        try { localStorage.setItem('theme', theme); } catch { }
    }
    function applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }

    const initialTheme = getStoredTheme();
    applyTheme(initialTheme);

    const toggle = document.getElementById('themeToggle');
    if (toggle) {
        toggle.addEventListener('click', () => {
            const current = document.documentElement.getAttribute('data-theme') || 'dark';
            const next = current === 'dark' ? 'light' : 'dark';
            applyTheme(next);
            storeTheme(next);
        });
    }

    // Footer year
    const year = document.getElementById('year');
    if (year) year.textContent = String(new Date().getFullYear());

    // Background animation using canvas
    const canvas = document.getElementById('bg');
    if (canvas && canvas.getContext) {
        const ctx = canvas.getContext('2d');
        let width, height, devicePixelRatio;
        const particles = [];
        const PARTICLE_COUNT = 80;

        function resize() {
            devicePixelRatio = window.devicePixelRatio || 1;
            width = canvas.clientWidth;
            height = canvas.clientHeight;
            canvas.width = Math.floor(width * devicePixelRatio);
            canvas.height = Math.floor(height * devicePixelRatio);
            ctx.setTransform(devicePixelRatio, 0, 0, devicePixelRatio, 0, 0);
        }
        window.addEventListener('resize', resize, { passive: true });
        resize();

        function spawnParticle() {
            const hues = [265, 200, 165]; // Purple, Cyan, Mint green
            return {
                x: Math.random() * width,
                y: Math.random() * height,
                r: Math.random() * 2 + 0.5,
                a: Math.random() * Math.PI * 2,
                s: Math.random() * 0.4 + 0.1,
                hue: hues[Math.floor(Math.random() * hues.length)]
            };
        }

        for (let i = 0; i < PARTICLE_COUNT; i++) particles.push(spawnParticle());

        function tick() {
            ctx.clearRect(0, 0, width, height);
            for (const p of particles) {
                p.a += 0.01;
                p.x += Math.cos(p.a) * p.s;
                p.y += Math.sin(p.a) * p.s;
                if (p.x < -10 || p.x > width + 10 || p.y < -10 || p.y > height + 10) {
                    const i = particles.indexOf(p);
                    particles[i] = spawnParticle();
                    continue;
                }
                const gradient = ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, p.r * 8);
                gradient.addColorStop(0, `hsla(${p.hue}, 95%, 65%, .9)`);
                gradient.addColorStop(1, `hsla(${p.hue}, 95%, 65%, 0)`);
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(p.x, p.y, p.r * 6, 0, Math.PI * 2);
                ctx.fill();
            }
            requestAnimationFrame(tick);
        }
        requestAnimationFrame(tick);
    }

    // Enhanced Portfolio Modal Functionality
    const portfolioModal = document.getElementById('portfolio-modal');
    const closeModal = document.querySelector('.close-modal');
    const modalTitle = document.querySelector('.modal-title');
    const modalDescription = document.querySelector('.modal-description');
    const modalTabs = document.querySelectorAll('.modal-tab');
    const modalTabContents = document.querySelectorAll('.modal-tab-content');
    const mediaOverlay = document.getElementById('media-overlay');
    const overlayMedia = document.getElementById('overlay-media');
    const overlayClose = document.querySelector('.overlay-close');
    const portfolioGrid = document.getElementById('portfolio-grid');

    // All projects data with new structure
    const allProjects = [
        {
            key: 'evotech',
            title: 'EvoTech Store',
            category: 'E-commerce',
            description: 'A modern E-commerce website for selling tech gadgets.',
            thumbnail: 'our work/evotech/landing page.mp4',
            landing: ['our work/evotech/landing page.mp4'],
            products: ['our work/evotech/products page.png'],
            details: ['our work/evotech/product details page.png']
        },
        {
            key: 'furniture',
            title: 'Furniture Store',
            category: 'E-commerce',
            description: 'An elegant e-commerce platform for premium furniture with seamless shopping experience.',
            thumbnail: 'our work/furniture/landing page.png',
            landing: ['our work/furniture/landing page.png'],
            products: ['our work/furniture/products page.png'],
            details: ['our work/furniture/product details page.png']
        },
        {
            key: 'pet',
            title: 'Pet Store',
            category: 'E-commerce',
            description: 'A comprehensive pet care platform connecting pet owners with what their pet needs.',
            thumbnail: 'our work/pet/landing page.mp4',
            landing: ['our work/pet/landing page.mp4'],
            products: ['our work/pet/products page.png'],
            details: ['our work/pet/product details page.png']
        },
        {
            key: 'shoes',
            title: 'Shoe Store',
            category: 'E-commerce',
            description: 'A dynamic e-commerce platform for footwear with advanced filtering and shopping features.',
            thumbnail: 'our work/shoes/landing page.mp4',
            landing: ['our work/shoes/landing page.mp4'],
            products: ['our work/shoes/products page.mp4'],
            details: ['our work/shoes/product details page.mp4']
        },
        {
            key: 'shoe1',
            title: 'Shoe Store 2',
            category: 'E-commerce',
            description: 'Premium shoe collection platform with advanced product showcase and filtering.',
            thumbnail: 'our work/SHOE 1/landing page.png',
            landing: ['our work/SHOE 1/landing page.png'],
            products: ['our work/SHOE 1/products page.png'],
            details: []
        },
        {
            key: 'shoe2',
            title: 'Shoe Store 3',
            category: 'E-commerce',
            description: 'Deluxe shoe shopping experience with comprehensive product details and reviews.',
            thumbnail: 'our work/SHOE 2/landing page.png',
            landing: ['our work/SHOE 2/landing page.png'],
            products: ['our work/SHOE 2/products page.png'],
            details: ['our work/SHOE 2/product details page.png']
        },
        {
            key: 'shoe3',
            title: 'Shoe Store 4',
            category: 'E-commerce',
            description: 'Elite shoe marketplace with video showcases and interactive product exploration.',
            thumbnail: 'our work/SHOE 3/landing page.mp4',
            landing: ['our work/SHOE 3/landing page.mp4'],
            products: ['our work/SHOE 3/products page.mp4'],
            details: ['our work/SHOE 3/product details page.png']
        },
        {
            key: 'sunglass',
            title: 'Sunglass Store',
            category: 'E-Commerce, Fashion',
            description: 'A stylish sunglasses store for premium sunglasses with immersive product visualization.',
            thumbnail: 'our work/Sunglass/landing page.png',
            landing: ['our work/Sunglass/landing page.png'],
            products: ['our work/Sunglass/products page.png'],
            details: []
        }
    ];

    // Portfolio pagination
    let currentPage = 0;
    const projectsPerPage = [3, 3, 2]; // First page: 3, Second page: 3, Third page: 2

    function renderPortfolioPage(pageIndex) {
        portfolioGrid.innerHTML = '';

        let startIndex = 0;
        for (let i = 0; i < pageIndex; i++) {
            startIndex += projectsPerPage[i];
        }

        const endIndex = startIndex + projectsPerPage[pageIndex];
        const pageProjects = allProjects.slice(startIndex, endIndex);

        pageProjects.forEach((project, index) => {
            const portfolioItem = document.createElement('div');
            portfolioItem.className = 'portfolio-item';
            portfolioItem.dataset.projectIndex = startIndex + index;

            const isVideo = project.thumbnail.endsWith('.mp4');
            const mediaElement = isVideo
                ? `<video src="${project.thumbnail}" muted loop autoplay class="portfolio-video"></video>`
                : `<img src="${project.thumbnail}" alt="${project.title}" class="portfolio-img">`;

            portfolioItem.innerHTML = `
                <div class="portfolio-image">
                    ${mediaElement}
                </div>
                <div class="portfolio-info">
                    <span class="portfolio-category">~ ${project.category}</span>
                    <h3 class="portfolio-project-title">${project.title}</h3>
                </div>
            `;

            portfolioItem.addEventListener('click', () => openProjectModal(startIndex + index));
            portfolioGrid.appendChild(portfolioItem);
        });
    }

    function openProjectModal(projectIndex) {
        const project = allProjects[projectIndex];

        modalTitle.textContent = project.title;
        modalDescription.textContent = project.description;

        // Load content for each tab
        loadTabContent('landing', project.landing);
        loadTabContent('products', project.products);
        loadTabContent('details', project.details);

        // Show first tab by default
        showTab('landing');

        portfolioModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    // Initialize portfolio
    renderPortfolioPage(0);

    // Tab functionality
    modalTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const tabName = tab.dataset.tab;
            showTab(tabName);
        });
    });

    function showTab(tabName) {
        modalTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        modalTabContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-content`);
        });
    }

    function loadTabContent(tabName, mediaPaths) {
        const container = document.getElementById(`${tabName}-content`);
        if (!container) return;

        container.innerHTML = '';

        if (!mediaPaths || mediaPaths.length === 0) {
            container.innerHTML = '<p class="no-content">No content available for this section.</p>';
            return;
        }

        mediaPaths.forEach(path => {
            const mediaContainer = document.createElement('div');
            mediaContainer.className = 'modal-media-item';

            if (path.endsWith('.mp4')) {
                const video = document.createElement('video');
                video.src = path;
                video.controls = true;
                video.muted = true;
                video.className = 'modal-video';
                mediaContainer.appendChild(video);
            } else {
                const img = document.createElement('img');
                img.src = path;
                img.alt = `${tabName} screenshot`;
                img.className = 'modal-image';
                mediaContainer.appendChild(img);
            }

            // Add click handler for expansion
            mediaContainer.addEventListener('click', () => {
                expandMedia(path);
            });

            container.appendChild(mediaContainer);
        });
    }

    function expandMedia(mediaPath) {
        overlayMedia.innerHTML = '';

        if (mediaPath.endsWith('.mp4')) {
            const video = document.createElement('video');
            video.src = mediaPath;
            video.controls = true;
            video.autoplay = true;
            video.className = 'overlay-video';
            overlayMedia.appendChild(video);
        } else {
            const img = document.createElement('img');
            img.src = mediaPath;
            img.className = 'overlay-image';
            overlayMedia.appendChild(img);
        }

        mediaOverlay.style.display = 'flex';
    }

    // Close modal when X is clicked
    if (closeModal && portfolioModal) {
        closeModal.addEventListener('click', () => {
            portfolioModal.style.display = 'none';
            document.body.style.overflow = '';
        });
    }

    // Close overlay when X is clicked
    if (overlayClose && mediaOverlay) {
        overlayClose.addEventListener('click', () => {
            mediaOverlay.style.display = 'none';
            // Pause any playing videos
            const videos = overlayMedia.querySelectorAll('video');
            videos.forEach(video => video.pause());
        });
    }

    // Close modal when clicking outside of modal content
    if (portfolioModal) {
        window.addEventListener('click', (e) => {
            if (e.target === portfolioModal) {
                portfolioModal.style.display = 'none';
                document.body.style.overflow = '';
            }
        });
    }

    // Close overlay when clicking outside of media content
    if (mediaOverlay) {
        window.addEventListener('click', (e) => {
            if (e.target === mediaOverlay) {
                mediaOverlay.style.display = 'none';
                const videos = overlayMedia.querySelectorAll('video');
                videos.forEach(video => video.pause());
            }
        });
    }

    // Skills progress bar animation
    const skillBars = document.querySelectorAll('.skill-progress');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const skillObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const width = progressBar.getAttribute('data-width');
                progressBar.style.width = width + '%';
                skillObserver.unobserve(progressBar);
            }
        });
    }, observerOptions);

    skillBars.forEach(bar => {
        skillObserver.observe(bar);
    });

    // FAQ Accordion functionality
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const isExpanded = question.getAttribute('aria-expanded') === 'true';

            faqQuestions.forEach(q => {
                if (q !== question) {
                    q.setAttribute('aria-expanded', 'false');
                }
            });

            question.setAttribute('aria-expanded', !isExpanded);
        });
    });

    // Contact form validation and submission
    const form = document.getElementById('contactForm');
    if (form) {
        const status = form.querySelector('.form-status');
        function showError(name, message) {
            const el = form.querySelector(`.error[data-for="${name}"]`);
            if (el) el.textContent = message;
        }
        function clearErrors() {
            form.querySelectorAll('.error').forEach((n) => (n.textContent = ''));
        }
        function validate(payload) {
            clearErrors();
            let ok = true;
            if (!payload.name || payload.name.length < 2) { showError('name', 'Please enter your full name.'); ok = false; }
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(payload.email)) { showError('email', 'Enter a valid email.'); ok = false; }
            if (payload.phone && !/^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/.test(payload.phone)) { showError('phone', 'Enter a valid phone number.'); ok = false; }
            if (!payload.message || payload.message.length < 10) { showError('message', 'Provide a few project details.'); ok = false; }
            return ok;
        }
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const data = new FormData(form);
            const payload = Object.fromEntries(data.entries());
            if (!validate(payload)) return;
            status.textContent = 'Sending…';

            const emailData = {
                to: '<EMAIL>',
                subject: `Web Dev Lab Contact: ${payload.name}`,
                body: `
                    Name: ${payload.name}
                    Email: ${payload.email}
                    Phone: ${payload.phone || 'Not provided'}
                    Message: ${payload.message}
                `
            };

            console.log('Sending email with data:', emailData);

            await new Promise((r) => setTimeout(r, 700));
            status.textContent = 'Thanks! We will be in touch within 24 hours.';
            form.reset();
        });
    }

    // Portfolio dots functionality
    const portfolioDots = document.querySelectorAll('.dot');

    portfolioDots.forEach((dot) => {
        dot.addEventListener('click', () => {
            const pageIndex = parseInt(dot.dataset.page);
            currentPage = pageIndex;

            portfolioDots.forEach(d => d.classList.remove('active'));
            dot.classList.add('active');

            renderPortfolioPage(pageIndex);
        });
    });
})();